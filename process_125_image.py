#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门处理125.jpg图片的人物裁剪脚本
实现以下功能：
1. 解析YOLO格式标注文件（0代表人，1代表人脸）
2. 为每个人框找到对应的人脸框
3. 更新人框的ymax为min(人框ymax, 人框ymin + 脸框高度*2.7)
4. 将人框放大1.2倍（保持中心点不变）
5. 扩展为16:9比例
6. 如果高度小于原图像高度的2/5，则扩大到原图像高度的2/5
7. 缩放为1920x1080并保存
"""

import cv2
import numpy as np
import os
import sys

# 导入现有的处理函数
from person_crop_processor import (
    parse_yolo_detection,
    yolo_to_bbox,
    find_matching_face,
    update_person_box_ymax,
    scale_box_by_center,
    expand_to_16_9_ratio,
    ensure_minimum_height
)

def process_125_image():
    """
    处理指定的125.jpg图片
    """
    # 指定的文件路径
    image_path = r"D:\Download\20250911-2\images\125.jpg"
    txt_path = r"D:\Download\20250911-2\images\125.txt"
    output_dir = r"D:\Download\20250911-2\person_crops_125_3"
    
    print("=" * 60)
    print("处理125.jpg图片的人物裁剪")
    print("=" * 60)
    print(f"图片路径: {image_path}")
    print(f"标注文件: {txt_path}")
    print(f"输出目录: {output_dir}")
    print()
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 错误: 图片文件不存在 {image_path}")
        return False
    
    if not os.path.exists(txt_path):
        print(f"❌ 错误: 标注文件不存在 {txt_path}")
        return False
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 错误: 无法读取图像文件 {image_path}")
        return False
    
    img_height, img_width = image.shape[:2]
    print(f"📐 图像尺寸: {img_width} x {img_height}")
    print(f"📏 图像高度的2/5: {img_height * 0.4:.1f} 像素")
    print()
    
    # 解析标注文件
    person_boxes = []
    face_boxes = []
    
    print("📋 解析标注文件...")
    with open(txt_path, 'r') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            detection = parse_yolo_detection(line)
            if detection is None:
                print(f"⚠️  警告: 第{line_num}行格式错误: {line}")
                continue
            
            class_id, center_x, center_y, width, height = detection
            
            # 转换为边界框坐标
            x1, y1, x2, y2 = yolo_to_bbox(center_x, center_y, width, height, img_width, img_height)
            
            # 确保坐标在图像范围内
            x1 = max(0, min(x1, img_width - 1))
            y1 = max(0, min(y1, img_height - 1))
            x2 = max(0, min(x2, img_width - 1))
            y2 = max(0, min(y2, img_height - 1))
            
            if class_id == 0:  # 人
                person_boxes.append((x1, y1, x2, y2))
                print(f"  👤 人框 {len(person_boxes)}: ({x1}, {y1}, {x2}, {y2}) 尺寸: {x2-x1}x{y2-y1}")
            elif class_id == 1:  # 人脸
                face_boxes.append((x1, y1, x2, y2))
                print(f"  😊 人脸框 {len(face_boxes)}: ({x1}, {y1}, {x2}, {y2}) 尺寸: {x2-x1}x{y2-y1}")
    
    print(f"\n📊 检测结果: {len(person_boxes)} 个人框，{len(face_boxes)} 个人脸框")
    
    if len(person_boxes) == 0:
        print("❌ 没有检测到人框，无法处理")
        return False
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")
    print()
    
    # 处理每个人框
    success_count = 0
    for i, person_box in enumerate(person_boxes):
        print(f"🔄 处理第 {i+1}/{len(person_boxes)} 个人框")
        print(f"  原始人框: {person_box}")
        
        # 1. 找到匹配的人脸框
        matching_face = find_matching_face(person_box, face_boxes)
        
        if matching_face is None:
            print(f"  ⚠️  警告: 未找到匹配的人脸框，跳过此人框")
            continue
        
        print(f"  匹配的人脸框: {matching_face}")
        
        # 2. 更新人框的ymax
        updated_person_box = update_person_box_ymax(person_box, matching_face)
        print(f"  更新ymax后: {updated_person_box}")
        
        # 3. 放大1.2倍（保持中心点不变）
        scaled_box = scale_box_by_center(updated_person_box, 1.2, img_width, img_height)
        print(f"  放大1.2倍后: {scaled_box}")
        
        # 4. 扩展为16:9比例
        ratio_box = expand_to_16_9_ratio(scaled_box, img_width, img_height)
        print(f"  扩展为16:9后: {ratio_box}")
        
        # 5. 确保高度至少为图像高度的2/5
        final_box = ensure_minimum_height(ratio_box, img_width, img_height, min_height_ratio=1/3)
        print(f"  最终框: {final_box}")
        
        # 6. 裁剪图像
        x1, y1, x2, y2 = final_box
        cropped_image = image[y1:y2+1, x1:x2+1]
        
        if cropped_image.size == 0:
            print(f"  ❌ 错误: 裁剪区域为空")
            continue
        
        print(f"  裁剪尺寸: {cropped_image.shape[1]}x{cropped_image.shape[0]}")
        
        # 7. 缩放为1920x1080
        resized_image = cv2.resize(cropped_image, (1920, 1080))
        print(f"  缩放为: 1920x1080")
        
        # 8. 保存结果
        output_filename = f"62_person_{i+1}.jpg"
        output_path = os.path.join(output_dir, output_filename)
        
        success = cv2.imwrite(output_path, resized_image)
        if success:
            print(f"  ✅ 保存成功: {output_filename}")
            success_count += 1
        else:
            print(f"  ❌ 保存失败: {output_filename}")
        
        print()
    
    print("=" * 60)
    print(f"🎉 处理完成! 成功处理 {success_count}/{len(person_boxes)} 个人框")
    print(f"📁 输出目录: {output_dir}")
    print("=" * 60)
    
    return success_count > 0

def main():
    """
    主函数
    """
    try:
        success = process_125_image()
        if success:
            print("\n✅ 程序执行成功!")
            return 0
        else:
            print("\n❌ 程序执行失败!")
            return 1
    except Exception as e:
        print(f"\n💥 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
