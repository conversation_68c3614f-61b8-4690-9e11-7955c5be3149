#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>
#include <regex>
#include "center_stage/czcv_center_stage.h"
#include "center_stage/center_stage_api.h"
#include "center_stage/person_viewer.h"
#include "base/bbox.h"

#include "hough_line_detector.h"
using namespace czcv_camera;



 void bgr2nv12(const cv::Mat & bgr, cv::Mat& nv12)
{
    cv::Mat yuv;
    cv::cvtColor(bgr, yuv, cv::COLOR_BGR2YUV_YV12);

    nv12 = cv::Mat::zeros(yuv.rows, yuv.cols, CV_8UC1);
    memcpy(nv12.data, yuv.data, bgr.rows * bgr.cols);
    unsigned char* pnv21v = (unsigned char*)nv12.data + bgr.rows * bgr.cols + 1;
    unsigned char* pnv21u = (unsigned char*)nv12.data + bgr.rows * bgr.cols;
    unsigned char* pyuv_v = (unsigned char*)yuv.data + bgr.rows * bgr.cols;
    unsigned char* pyuv_u = (unsigned char*)yuv.data + bgr.rows * bgr.cols + bgr.rows * bgr.cols / 4;
    for (size_t i = 0; i < bgr.rows * bgr.cols / 4; i++)
    {
        *pnv21u = *pyuv_v;
        *pnv21v = *pyuv_u;

        pyuv_v++;
        pyuv_u++;
        pnv21v += 2;
        pnv21u += 2;
    }
}


// 结构体定义
struct DebugBox {
    float xmin, ymin, xmax, ymax, score;
    int classid, instanceid;
    
    DebugBox() : xmin(0), ymin(0), xmax(0), ymax(0), score(0), classid(0), instanceid(0) {}
    DebugBox(float x1, float y1, float x2, float y2, float score_, int cid, int iid)
        : xmin(x1), ymin(y1), xmax(x2), ymax(y2), score(score_), classid(cid), instanceid(iid) {}
};

struct Gesture {
    float xmin, ymin, xmax, ymax, score;
    int classid, instanceid;
    
    Gesture() : xmin(0), ymin(0), xmax(0), ymax(0), score(0), classid(0), instanceid(0) {}
    Gesture(float x1, float y1, float x2, float y2, float score_, int cid, int iid)
        : xmin(x1), ymin(y1), xmax(x2), ymax(y2), score(score_), classid(cid), instanceid(iid) {}
};

struct FrameData {
    std::string timestamp;
    std::vector<DebugBox> in_bbox;
    std::vector<DebugBox> tracked_bbox;
    std::vector<Gesture> gesture;
    float doa;
    std::vector<DebugBox> sub_in_bbox;
    std::vector<DebugBox> sub_tracked_bbox;
    float doa_sub;
    
    FrameData() : doa(-1.0f), doa_sub(-1.0f) {}
};

class CZCVDebugParser {
private:
    // 解析数字序列为BoundingBox向量
    std::vector<DebugBox> parseBoundingBoxes(const std::string& data) {
        std::vector<DebugBox> boxes;
        if (data.empty()) return boxes;
        
        std::istringstream iss(data);
        std::vector<float> values;
        std::string token;
        
        // 分割数字
        while (iss >> token) {
            try {
                values.push_back(std::stof(token));
            } catch (const std::exception& e) {
                // 忽略无法转换的字符串
                continue;
            }
        }
        
        // 每6个数字组成一个box
        for (size_t i = 0; i + 7 <= values.size(); i += 7) {
            boxes.emplace_back(values[i], values[i+1], values[i+2], 
                             values[i+3], values[i + 4], (int)values[i+5], (int)values[i+6]);
        }
        
        return boxes;
    }
    
    // 解析数字序列为Gesture向量
    std::vector<Gesture> parseGestures(const std::string& data) {
        std::vector<Gesture> gestures;
        if (data.empty()) return gestures;
        
        std::istringstream iss(data);
        std::vector<float> values;
        std::string token;
        
        // 分割数字
        while (iss >> token) {
            try {
                values.push_back(std::stof(token));
            } catch (const std::exception& e) {
                // 忽略无法转换的字符串
                continue;
            }
        }
        
        // 每6个数字组成一个gesture
        for (size_t i = 0; i + 7 <= values.size(); i += 7) {
            gestures.emplace_back(values[i], values[i+1], values[i+2], 
                                values[i+3], values[i + 4], (int)values[i+5], (int)values[i+6]);
        }
        
        return gestures;
    }
    
    // 提取字段值
    std::string extractFieldValue(const std::string& line, const std::string& fieldName) {
        size_t pos = line.find(fieldName + ":");
        if (pos == std::string::npos) return "";
        
        pos += fieldName.length() + 1; // 跳过字段名和冒号
        
        // 找到下一个分号或行尾
        size_t endPos = line.find(';', pos);
        if (endPos == std::string::npos) {
            endPos = line.length();
        }
        
        std::string value = line.substr(pos, endPos - pos);
        
        // 去除前后空格
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);
        
        return value;
    }
    
    // 提取时间戳
    std::string extractTimestamp(const std::string& line) {
        // 时间戳格式: 09-05 13:56:09.129
        std::regex timestampRegex(R"((\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}))");
        std::smatch match;
        
        if (std::regex_search(line, match, timestampRegex)) {
            return match[1].str();
        }
        
        return "";
    }

public:
    // 解析单行数据
    FrameData parseLine(const std::string& line) {
        FrameData frame;
        
        // 提取时间戳
        frame.timestamp = extractTimestamp(line);
        
        // 提取各个字段
        std::string in_bbox_str = extractFieldValue(line, "in_bbox");
        std::string tracked_bbox_str = extractFieldValue(line, "tracked_bbox");
        std::string gesture_str = extractFieldValue(line, "gesture");
        std::string doa_str = extractFieldValue(line, "doa");
        std::string sub_in_bbox_str = extractFieldValue(line, "sub_in_bbox");
        std::string sub_tracked_bbox_str = extractFieldValue(line, "sub_tracked_bbox");
        std::string doa_sub_str = extractFieldValue(line, "doa_sub");
        
        // 解析数据
        frame.in_bbox = parseBoundingBoxes(in_bbox_str);
        frame.tracked_bbox = parseBoundingBoxes(tracked_bbox_str);
        frame.gesture = parseGestures(gesture_str);
        frame.sub_in_bbox = parseBoundingBoxes(sub_in_bbox_str);
        frame.sub_tracked_bbox = parseBoundingBoxes(sub_tracked_bbox_str);

        // 解析DOA值
        if (!doa_str.empty()) {
            try {
                frame.doa = std::stof(doa_str);
            } catch (const std::exception& e) {
                frame.doa = -1.0f;
            }
        }
        
        if (!doa_sub_str.empty()) {
            try {
                frame.doa_sub = std::stof(doa_sub_str);
            } catch (const std::exception& e) {
                frame.doa_sub = -1.0f;
            }
        }
        
        return frame;
    }
    
    // 解析整个文件
    std::vector<FrameData> parseFile(const std::string& filename) {
        std::vector<FrameData> frames;
        std::ifstream file(filename);
        
        if (!file.is_open()) {
            std::cerr << "open file failed: " << filename << std::endl;
            return frames;
        }
        
        std::string line;
        while (std::getline(file, line)) {
            if (!line.empty()) {
                frames.push_back(parseLine(line));
            }
        }
        
        file.close();
        return frames;
    }
    
    // 打印BoundingBox信息
    void printBoundingBox(const DebugBox& box, const std::string& prefix = "") {
        std::cout << prefix << "Box: (" << box.xmin << ", " << box.ymin 
                  << ", " << box.xmax << ", " << box.ymax 
                  << ") class=" << box.classid << " instance=" << box.instanceid << std::endl;
    }
    
    // 打印Gesture信息
    void printGesture(const Gesture& gesture, const std::string& prefix = "") {
        std::cout << prefix << "Gesture: (" << gesture.xmin << ", " << gesture.ymin 
                  << ", " << gesture.xmax << ", " << gesture.ymax 
                  << ") class=" << gesture.classid << " instance=" << gesture.instanceid << std::endl;
    }
    
    // 打印帧数据
    void printFrameData(const FrameData& frame) {
        std::cout << "=== Frame: " << frame.timestamp << " ===" << std::endl;
        
        std::cout << "in_bbox (" << frame.in_bbox.size() << " boxes):" << std::endl;
        for (const auto& box : frame.in_bbox) {
            printBoundingBox(box, "  ");
        }
        
        std::cout << "tracked_bbox (" << frame.tracked_bbox.size() << " boxes):" << std::endl;
        for (const auto& box : frame.tracked_bbox) {
            printBoundingBox(box, "  ");
        }
        
        std::cout << "gesture (" << frame.gesture.size() << " gestures):" << std::endl;
        for (const auto& gesture : frame.gesture) {
            printGesture(gesture, "  ");
        }
        
        std::cout << "doa: " << frame.doa << std::endl;
        
        std::cout << "sub_in_bbox (" << frame.sub_in_bbox.size() << " boxes):" << std::endl;
        for (const auto& box : frame.sub_in_bbox) {
            printBoundingBox(box, "  ");
        }
        
        std::cout << "sub_tracked_bbox (" << frame.sub_tracked_bbox.size() << " boxes):" << std::endl;
        for (const auto& box : frame.sub_tracked_bbox) {
            printBoundingBox(box, "  ");
        }
        
        std::cout << "doa_sub: " << frame.doa_sub << std::endl;
        std::cout << std::endl;
    }
};

int main() {
    const float PI = 3.141592653f;
    const float INV_SQRT_3 = 0.75355f;
    const float LONG_INV_SQRT_3 = 2.1445f;
    const float LONG_SQRT_3 = 0.4663f; //tan(25)
    const float SQRT_3 = 1.327f;


    czcv_camera::Android_API* cameraAlg = new czcv_camera::Android_API();
    std::string modelpath = "";
    int src_width = 3840;
    int src_height = 2160;
    int dst_width = 3840;    
    int dst_height = 2160;
    bool only_cpu = false;
    bool external_alg = false;
    int gesture_mode = 1;
    czcv_doa_callback pfun_doa_callback = nullptr;
    czcv_gesture_event_callback pfun_gesture_event_callback = nullptr;
    czcv_camera_led_callback pfun_camera_led_callback = nullptr;
    int ret = cameraAlg->init_api(modelpath, src_width, src_height, dst_width, dst_height, true, only_cpu,external_alg, gesture_mode, pfun_doa_callback, pfun_gesture_event_callback, pfun_camera_led_callback);
    if (ret != 0)
    {
        LOGE("init_api failed\n");
        return -1;
    }
    cameraAlg->set_mode(enTrackModeVideoandAudio);

    /*float x0 = 2416.437744;
    float x1 = 3646.710938;
    float xc = (x0 + x1) * 0.5f;
    float angleout = (-atanf(SQRT_3 * (xc - src_width * 0.5f) / (src_width * 0.5f))) * 180 / PI + 270;
    std::cout << "angleout: " << angleout << std::endl;
    return 1;*/

    CZCVDebugParser parser;

    // 解析文件
    std::vector<FrameData> frames = parser.parseFile("D:/Download/25-09-11_19-01-25/AlgorithmCameraLog/czcv_debug.txt");

    // 打印前几帧数据作为示例
    int maxFramesToShow = 3;
    for (int i = 0; i < std::min(maxFramesToShow, (int)frames.size()); ++i) {
        parser.printFrameData(frames[i]);
    }

    // 统计信息
    int totalInBbox = 0, totalTrackedBbox = 0, totalGesture = 0;
    int totalSubInBbox = 0, totalSubTrackedBbox = 0;

    for (const auto& frame : frames) {
        totalInBbox += frame.in_bbox.size();
        totalTrackedBbox += frame.tracked_bbox.size();
        totalGesture += frame.gesture.size();
        totalSubInBbox += frame.sub_in_bbox.size();
        totalSubTrackedBbox += frame.sub_tracked_bbox.size();
    }

    std::cout << "=== stat ===" << std::endl;
    std::cout << "total frame: " << frames.size() << std::endl;
    std::cout << "total in_bbox count: " << totalInBbox << std::endl;
    std::cout << "total tracked_bbox count: " << totalTrackedBbox << std::endl;
    std::cout << "total gesture count: " << totalGesture << std::endl;
    std::cout << "total sub_in_bbox count: " << totalSubInBbox << std::endl;
    std::cout << "total sub_tracked_bbox count: " << totalSubTrackedBbox << std::endl;
    int startidx = 0;
    int idx = -1;
    cv::namedWindow("debug", 0);
    for (const auto& frame : frames)
    {
        idx++;
        if (idx < startidx)
        {
            continue;
        }
        
        cv::Mat bgr = cv::Mat::zeros(src_height, src_width, CV_8UC3);
        cv::Mat nv12img;
        bgr2nv12(bgr, nv12img);
        int _down_scale = 3;
        int _down_scale_sub = 12;
        TrackerInputOutput t;
        for (auto& b : frame.in_bbox)
        {
            _Bbox<float> box(b.xmin/ _down_scale, b.ymin / _down_scale, b.xmax / _down_scale, b.ymax / _down_scale, b.score, b.classid, b.instanceid);
            t.push_in_bbox(box);
        }
        for (auto& b : frame.tracked_bbox)
        {
            _Bbox<float> box(b.xmin / _down_scale, b.ymin / _down_scale, b.xmax / _down_scale, b.ymax / _down_scale, b.score, b.classid, b.instanceid);
            t.push_tracked_bbox(box);
        }
        std::vector<stGestureRecResult>& gestures = t.gestureResults();
        for (auto& b : frame.gesture)
        {
            stGestureRecResult g;
            g.rectf.x0 = b.xmin / _down_scale;
            g.rectf.y0 = b.ymin / _down_scale;
            g.rectf.x1 = b.xmax / _down_scale;
            g.rectf.y1 = b.ymax / _down_scale;
            g.clsid = b.classid;
            g.det_instance_id = b.instanceid;
            g.palm_score = b.score;
            gestures.push_back(g);
        }
        TrackerInputOutput t_sub;
        for (auto& b : frame.sub_in_bbox)
        {
            _Bbox<float> box(b.xmin / _down_scale_sub, b.ymin / _down_scale_sub, b.xmax / _down_scale_sub, b.ymax / _down_scale_sub, b.score, b.classid, b.instanceid);
            t_sub.push_in_bbox(box);
        }
        for (auto& b : frame.sub_tracked_bbox)
        {
            _Bbox<float> box(b.xmin / _down_scale_sub, b.ymin / _down_scale_sub, b.xmax / _down_scale_sub, b.ymax / _down_scale_sub, b.score, b.classid, b.instanceid);
            t_sub.push_tracked_bbox(box);
        }
        float doa = frame.doa;
        float doa_sub = frame.doa_sub;

        cameraAlg->set_debug_info(t, t_sub, doa, doa_sub);

        int sub_phy_addr = 1;
        ret = cameraAlg->run_api_sub(nv12img, sub_phy_addr);

        int phy_addr = 0;
        void* dst_vir_addr = nullptr;
        int dst_phy_addr = -1;
        int Cmode = 1;
        int low_consumption = 0;
        ret = cameraAlg->run_api(nv12img, phy_addr, dst_vir_addr, dst_phy_addr, Cmode, low_consumption);

        cv::putText(bgr, frame.timestamp, cv::Point(src_width - 800, 80), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 255, 0), 3, 8, 0);

        if (g_debug_roi)
        {
            cv::putText(bgr, std::string("S"), cv::Point(120, 240), cv::FONT_HERSHEY_SIMPLEX, 3, cv::Scalar(0, 255, 0), 4, 8, 0);
        }
        
        if (g_debug_phy_addr == phy_addr)
        {
            if (frame.doa >= 0)
            {
                int xcoor = (-tanf((frame.doa - 270) * PI / 180) * INV_SQRT_3 * src_width * 0.5f + src_width * 0.5f);
                cv::line(bgr, cv::Point(src_width - 1 - xcoor, 0), cv::Point(src_width - 1 - xcoor, src_height - 1), cv::Scalar(255, 255, 255), 3);
            }

            auto& in_bbox = frame.in_bbox;
            for (auto& box : in_bbox)
            {
                cv::rectangle(bgr, cv::Rect(src_width - 1 - box.xmax, box.ymin, box.xmax - box.xmin, box.ymax - box.ymin), cv::Scalar(0, 255, 0), 2, 8, 0);
                cv::putText(bgr, std::to_string(box.classid), cv::Point(src_width - 1 - box.xmax, box.ymin - 10), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 255, 0), 2, 8, 0);
                cv::putText(bgr, std::to_string(box.instanceid), cv::Point(src_width - 1 - box.xmax + 50, box.ymin - 10), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 255, 0), 2, 8, 0);
                cv::putText(bgr, std::to_string(box.score), cv::Point(src_width - 1 - box.xmax + 200, box.ymin - 10), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 255, 0), 2, 8, 0);
            }

            auto& tracked_bbox = frame.tracked_bbox;
            for (auto& box : tracked_bbox)
            {
                //std::cout << "tracked_bbox: " << box.xmin << " " << box.ymin << " " << box.xmax << " " << box.ymax << std::endl;
                cv::rectangle(bgr, cv::Rect(src_width - 1 - box.xmax, box.ymin, box.xmax - box.xmin, box.ymax - box.ymin), cv::Scalar(255, 0, 0), 2, 8, 0);
            }

            //std::cout << g_debug_bbox.xmin() << " " << g_debug_bbox.ymin() << " " << g_debug_bbox.xmax() << " " << g_debug_bbox.ymax() << std::endl;
            cv::rectangle(bgr, cv::Rect(src_width - 1 - g_debug_bbox.xmax(), g_debug_bbox.ymin(), g_debug_bbox.xmax() - g_debug_bbox.xmin(), g_debug_bbox.ymax() - g_debug_bbox.ymin()), cv::Scalar(255, 255, 255), 6, 8, 0);
       
        }
        else
        {
            if (frame.doa_sub >= 0)
            {
                int xcoor = (-tanf((frame.doa_sub  - 270) * PI / 180) * LONG_INV_SQRT_3 * src_width * 0.5f + src_width * 0.5f);
                std::cout << "xcoor: " << xcoor << std::endl;
                if (xcoor >= 0 && xcoor < src_width)
                {
                    cv::line(bgr, cv::Point(src_width - 1 - xcoor, 0), cv::Point(src_width - 1 - xcoor, src_height - 1), cv::Scalar(255, 255, 255), 3);
                }    
            }

            cv::putText(bgr, std::string("L"), cv::Point(120, 120), cv::FONT_HERSHEY_SIMPLEX, 3, cv::Scalar(0, 255, 0), 4, 8, 0);
            
            auto& in_bbox = frame.sub_in_bbox;
            for (auto& box : in_bbox)
            {
                cv::rectangle(bgr, cv::Rect(src_width - 1 - box.xmax, box.ymin, box.xmax - box.xmin, box.ymax - box.ymin), cv::Scalar(0, 255, 0), 2, 8, 0);
                cv::putText(bgr, std::to_string(box.classid), cv::Point(src_width - 1 - box.xmax, box.ymin - 10), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 255, 0), 2, 8, 0);
                cv::putText(bgr, std::to_string(box.instanceid), cv::Point(src_width - 1 - box.xmax + 50, box.ymin - 10), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 255, 0), 2, 8, 0);
                cv::putText(bgr, std::to_string(box.score), cv::Point(src_width - 1 - box.xmax + 200, box.ymin - 10), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 255, 0), 2, 8, 0);
            }

            auto& tracked_bbox = frame.sub_tracked_bbox;
            for (auto& box : tracked_bbox)
            {
                //std::cout << "tracked_bbox: " << box.xmin << " " << box.ymin << " " << box.xmax << " " << box.ymax << std::endl;
                cv::rectangle(bgr, cv::Rect(src_width - 1 - box.xmax, box.ymin, box.xmax - box.xmin, box.ymax - box.ymin), cv::Scalar(255, 0, 0), 2, 8, 0);
            }

            //std::cout << g_debug_bbox.xmin() << " " << g_debug_bbox.ymin() << " " << g_debug_bbox.xmax() << " " << g_debug_bbox.ymax() << std::endl;
            cv::rectangle(bgr, cv::Rect(src_width - 1 - g_debug_bbox.xmax(), g_debug_bbox.ymin(), g_debug_bbox.xmax() - g_debug_bbox.xmin(), g_debug_bbox.ymax() - g_debug_bbox.ymin()), cv::Scalar(255, 255, 255), 6, 8, 0);
        }

        cv::imshow("debug", bgr);
        cv::waitKey(67);
    }
    

    return 0;
}
