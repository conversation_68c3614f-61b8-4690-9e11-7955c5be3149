﻿  czcv_center_stage.cpp
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(44,11): warning C4251: “czcv_camera::ImageBlob::frameCopiedBGR”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::ImageBlob”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(97,11): warning C4251: “czcv_camera::Any::m_ptr”: class“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(67): message : 参见“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(98,19): warning C4251: “czcv_camera::Any::m_tpIndex”: class“std::type_index”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\typeindex(25): message : 参见“std::type_index”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(117,14): warning C4251: “czcv_camera::DynamicParams::_mu”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(168,31): warning C4251: “czcv_camera::DynamicParams::_keyValues”: class“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(162): message : 参见“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/status.h(46,21): warning C4251: “czcv_camera::Status::message_”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::Status”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/center_stage_capi.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(39,21): warning C4251: “czcv_camera::AbstarctModel::_profileData”: class“czcv_camera::ProfileData”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base\profile_data.h(9): message : 参见“czcv_camera::ProfileData”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(41,34): warning C4251: “czcv_camera::AbstarctModel::_modelConfig”: class“std::vector<cv::String,std::allocator<cv::String>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/persistence.hpp(417): message : 参见“std::vector<cv::String,std::allocator<cv::String>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(45,21): warning C4251: “czcv_camera::AbstarctModel::_modelName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(24,17): warning C4251: “czcv_camera::DetInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30,29): warning C4251: “czcv_camera::DetInputOutput::_bbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(31,49): warning C4251: “czcv_camera::DetInputOutput::_landmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32,49): warning C4251: “czcv_camera::DetInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(59,17): warning C4251: “czcv_camera::TrackerInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(60,29): warning C4251: “czcv_camera::TrackerInputOutput::_inBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(67,29): warning C4251: “czcv_camera::TrackerInputOutput::_trackedBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(68,49): warning C4251: “czcv_camera::TrackerInputOutput::_trackedLandmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(69,42): warning C4251: “czcv_camera::TrackerInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74,41): warning C4251: “czcv_camera::TrackerInputOutput::_gestureResults”: class“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74): message : 参见“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(261,45): warning C4251: “czcv_camera::BaseTracker::_detector”: class“std::shared_ptr<czcv_camera::BaseObjectDetector>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/detector_factory.h(65): message : 参见“std::shared_ptr<czcv_camera::BaseObjectDetector>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(268,19): warning C4251: “czcv_camera::BaseTracker::_lastRoi”: class“cv::Rect_<int>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/types.hpp(459): message : 参见“cv::Rect_<int>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(107,20): warning C4251: “czcv_camera::PoolAllocator::budgets_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(108,20): warning C4251: “czcv_camera::PoolAllocator::payouts_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110,46): warning C4251: “czcv_camera::PoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(111,46): warning C4251: “czcv_camera::PoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(132,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(133,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(57,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapDtoS”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(58,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapStoD”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(67,5): warning C4275: 非 dll 接口 class“czcv_camera::Abstarct_PersonViewer_DataCallback”用作 dll 接口 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的基
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(24): message : 参见“czcv_camera::Abstarct_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(66): message : 参见“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(140,17): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(146,33): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_cv”: class“std::condition_variable”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(583): message : 参见“std::condition_variable”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(147,20): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_mtx”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(210,42): warning C4251: “czcv_camera::Base_PersonViewer::_camDewarperPtr”: class“std::shared_ptr<czcv_camera::BaseCamDewarper>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(165): message : 参见“std::shared_ptr<czcv_camera::BaseCamDewarper>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(211,62): warning C4251: “czcv_camera::Base_PersonViewer::_dataCallbackPtr”: class“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(171): message : 参见“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(212,42): warning C4251: “czcv_camera::Base_PersonViewer::_rgaInterfacePtr”: class“std::shared_ptr<czcv_camera::rga_interface_t>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(27): message : 参见“std::shared_ptr<czcv_camera::rga_interface_t>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(215,37): warning C4251: “czcv_camera::Base_PersonViewer::_infos”: class“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(215): message : 参见“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(224,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapx”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(225,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapy”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(81,49): warning C4251: “czcv_camera::PersonCenterStager::_impl”: class“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”需要有 dll 接口由 class“czcv_camera::PersonCenterStager”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(81): message : 参见“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”的声明
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\../utils/async_runner.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\config/config_setter.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\tracker/person_assert/rknn_person_assert.h(80,26): warning C4305: “初始化”: 从“double”到“float”截断
D:\Program\Project\project\czcv_camera_new\lib\src\detector/detect_white_board.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  cl_version.h: CL_TARGET_OPENCL_VERSION is not defined. Defaulting to 220 (OpenCL 2.2)
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(55,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(63,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(69,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(85,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(164,17): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(776,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1466,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2078,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2672,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(409,46): warning C4244: “参数”: 从“const double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(459,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(460,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(461,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(462,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(489,56): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(489,38): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(541,27): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(544,31): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(545,35): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(560,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(570,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(632,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(652,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(715,31): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(718,35): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(719,39): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(734,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(744,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(794,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(813,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(921,41): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(945,41): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1146,70): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1148,70): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1150,68): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1152,68): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1229,30): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1230,30): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1234,33): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1242,41): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1243,42): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1251,42): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1263,33): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1271,40): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1272,43): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1280,42): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1291,96): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1291,78): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1458,63): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1458,50): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1459,79): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1459,61): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1461,28): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1468,25): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1476,32): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1477,35): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1483,22): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1484,22): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1488,25): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1496,33): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1497,34): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1505,34): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1517,25): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1525,32): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1526,35): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1534,34): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1549,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1549,44): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1575,86): warning C4244: “参数”: 从“int”转换到“const float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1853,72): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1853,56): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2136,1): warning C4305: “参数”: 从“double”到“float”截断
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2173,63): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2173,50): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2176,33): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2182,33): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2233,50): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2233,39): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2253,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2253,51): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2617,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2622,58): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2622,47): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2643,70): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2643,59): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2719,80): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2719,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2727,45): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2727,34): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2760,41): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2815,83): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2815,65): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2820,50): warning C4244: “=”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2833,29): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2841,36): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2842,39): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2848,26): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2849,26): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2853,29): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2861,37): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2862,38): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2870,38): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2882,29): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2890,36): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2891,39): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2899,38): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2914,66): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2914,48): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2998,79): warning C4244: “参数”: 从“int”转换到“const float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3221,58): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3221,49): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4152,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4803,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5273,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5913,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6556,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4486,106): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4486,77): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4630,109): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4630,81): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4815,47): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4816,47): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4824,80): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4824,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4884,80): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4884,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4933,65): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4934,65): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5005,69): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5006,65): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5019,31): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5020,31): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5023,60): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5023,57): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5029,107): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5029,91): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5030,127): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5030,111): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5030,84): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5030,68): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5042,127): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5042,111): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5042,84): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5042,68): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5050,119): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5050,98): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5199,69): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5200,65): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5690,1): warning C4244: “*=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5691,1): warning C4244: “*=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5835,38): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5868,135): warning C4244: “=”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5870,90): warning C4244: “=”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5876,63): warning C4244: “=”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5877,63): warning C4244: “=”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5878,63): warning C4244: “=”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5879,63): warning C4244: “=”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5896,32): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6041,42): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6042,56): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6318,36): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6335,42): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6357,44): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6359,44): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6456,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(140,21): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(139): message : 在编译 类 模板 成员函数“float czcv_camera::_Bbox<float>::distance_with(czcv_camera::_Bbox<float> &)”时
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2423): message : 查看对正在编译的函数 模板 实例化“float czcv_camera::_Bbox<float>::distance_with(czcv_camera::_Bbox<float> &)”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(106): message : 查看对正在编译的 类 模板 实例化“czcv_camera::_Bbox<float>”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(141,21): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,84): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58): message : 在编译 类 模板 成员函数“cv::Rect czcv_camera::_Bbox<float>::cv_rect(void) const”时
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(921): message : 查看对正在编译的函数 模板 实例化“cv::Rect czcv_camera::_Bbox<float>::cv_rect(void) const”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,74): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,65): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,58): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
    正在创建库 D:/Program/Project/project/czcv_camera_new/build_windows/lib/Release/czcv_camera.lib 和对象 D:/Program/Project/project/czcv_camera_new/build_windows/lib/Release/czcv_camera.exp
  czcv_camera.vcxproj -> D:\Program\Project\project\czcv_camera_new\output\x86\bin\Release\czcv_camera.dll
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
