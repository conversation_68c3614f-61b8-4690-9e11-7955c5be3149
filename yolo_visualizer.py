import cv2
import numpy as np
import os
import argparse
import math

def parse_yolo_detection(line):
    """
    解析YOLO检测结果的一行
    格式: class_id center_x center_y width height
    返回: (class_id, center_x, center_y, width, height)
    """
    parts = line.strip().split()
    if len(parts) != 5:
        return None
    
    class_id = int(parts[0])
    center_x = float(parts[1])
    center_y = float(parts[2])
    width = float(parts[3])
    height = float(parts[4])
    
    return class_id, center_x, center_y, width, height

def yolo_to_bbox(center_x, center_y, width, height, img_width, img_height):
    """
    将YOLO格式的归一化坐标转换为边界框坐标
    """
    # 转换为像素坐标
    center_x_px = center_x * img_width
    center_y_px = center_y * img_height
    width_px = width * img_width
    height_px = height * img_height
    
    # 计算边界框的左上角和右下角坐标
    x1 = int(center_x_px - width_px / 2)
    y1 = int(center_y_px - height_px / 2)
    x2 = int(center_x_px + width_px / 2)
    y2 = int(center_y_px + height_px / 2)
    
    return x1, y1, x2, y2

def visualize_yolo_detections(image_path, txt_path, output_path=None, class_names=None):
    """
    可视化YOLO检测结果
    
    Args:
        image_path: 图像文件路径
        txt_path: YOLO检测结果文件路径
        output_path: 输出图像路径，如果为None则显示图像
        class_names: 类别名称列表，如果为None则使用类别ID
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图像文件 {image_path}")
        return False
    
    img_height, img_width = image.shape[:2]
    print(f"图像尺寸: {img_width} x {img_height}")
    
    # 读取检测结果
    if not os.path.exists(txt_path):
        print(f"错误: 检测结果文件不存在 {txt_path}")
        return False
    
    # 定义颜色（BGR格式）
    colors = [
        (0, 255, 0),    # 绿色 - class 0
        (255, 0, 0),    # 蓝色 - class 1
        (0, 0, 255),    # 红色 - class 2
        (255, 255, 0),  # 青色 - class 3
        (255, 0, 255),  # 品红色 - class 4
        (0, 255, 255),  # 黄色 - class 5
    ]
    
    detection_count = 0
    
    with open(txt_path, 'r') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            detection = parse_yolo_detection(line)
            if detection is None:
                print(f"警告: 第{line_num}行格式错误: {line}")
                continue
            
            class_id, center_x, center_y, width, height = detection
            
            # 转换为边界框坐标
            x1, y1, x2, y2 = yolo_to_bbox(center_x, center_y, width, height, img_width, img_height)
            
            # 确保坐标在图像范围内
            x1 = max(0, min(x1, img_width - 1))
            y1 = max(0, min(y1, img_height - 1))
            x2 = max(0, min(x2, img_width - 1))
            y2 = max(0, min(y2, img_height - 1))
            
            # 选择颜色
            color = colors[class_id % len(colors)]
            
            # 绘制边界框
            cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
            
            # 准备标签文本
            if class_names and class_id < len(class_names):
                label = f"{class_names[class_id]}: {class_id}"
            else:
                label = f"Class {class_id}"
            
            # 计算文本尺寸
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            thickness = 2
            (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
            
            # 绘制文本背景
            cv2.rectangle(image, (x1, y1 - text_height - 10), (x1 + text_width, y1), color, -1)
            
            # 绘制文本
            cv2.putText(image, label, (x1, y1 - 5), font, font_scale, (255, 255, 255), thickness)
            
            detection_count += 1
            print(f"检测 {detection_count}: Class {class_id}, 坐标 ({x1}, {y1}, {x2}, {y2})")
    
    print(f"总共检测到 {detection_count} 个目标")
    
    # 保存或显示结果
    if output_path:
        success = cv2.imwrite(output_path, image)
        if success:
            print(f"可视化结果已保存到: {output_path}")
        else:
            print(f"错误: 无法保存图像到 {output_path}")
        return success
    else:
        # 显示图像
        cv2.imshow('YOLO Detection Results', image)
        print("按任意键关闭窗口...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        return True

def main():
    parser = argparse.ArgumentParser(description='可视化YOLO检测结果')
    parser.add_argument('--image', required=True, help='输入图像路径')
    parser.add_argument('--txt', required=True, help='YOLO检测结果文件路径')
    parser.add_argument('--output', help='输出图像路径（可选）')
    parser.add_argument('--classes', help='类别名称文件路径（可选）')
    
    args = parser.parse_args()
    
    # 读取类别名称（如果提供）
    class_names = None
    if args.classes and os.path.exists(args.classes):
        with open(args.classes, 'r', encoding='utf-8') as f:
            class_names = [line.strip() for line in f if line.strip()]
    
    # 执行可视化
    success = visualize_yolo_detections(args.image, args.txt, args.output, class_names)
    
    if not success:
        print("可视化失败")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys

    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        # 使用命令行参数
        main()
    else:
        # 如果直接运行脚本，处理指定目录下的所有图像
        base_dir = r"D:\Download\20250911-2\images"

        # 查找所有jpg文件
        for filename in os.listdir(base_dir):
            if filename.lower().endswith('.jpg'):
                image_name = os.path.splitext(filename)[0]
                image_path = os.path.join(base_dir, filename)
                txt_path = os.path.join(base_dir, f"{image_name}.txt")
                output_path = os.path.join(base_dir, f"{image_name}_visualized.jpg")

                if os.path.exists(txt_path):
                    print(f"\n开始处理: {filename}")
                    print(f"图像文件: {image_path}")
                    print(f"检测结果文件: {txt_path}")
                    print(f"输出文件: {output_path}")

                    success = visualize_yolo_detections(image_path, txt_path, output_path)

                    if success:
                        print(f"✓ {filename} 可视化完成!")
                    else:
                        print(f"✗ {filename} 可视化失败!")
                else:
                    print(f"跳过 {filename}: 找不到对应的txt文件")

        print("\n所有文件处理完成!")
